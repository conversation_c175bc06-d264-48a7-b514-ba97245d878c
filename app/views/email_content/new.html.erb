<% content_for :title, "Create Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-16">
        <div class="flex items-center space-x-4">
          <%= link_to campaign_path(@campaign), 
              class: "flex items-center justify-center w-8 h-8 rounded-full bg-slate-100 hover:bg-slate-200 transition-colors" do %>
            <svg class="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          
          <div>
            <h1 class="text-xl font-bold text-slate-900">Create Email Content</h1>
            <p class="text-sm text-slate-600">Campaign: <%= @campaign.name %></p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Email Campaign Details</h2>
      </div>

      <div class="p-6">
        <%= form_with model: @email_campaign, url: campaign_email_content_path(@campaign), local: true, class: "space-y-6" do |form| %>
          
          <!-- AI Generated Content Indicator -->
          <% if @showing_generated_content %>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-green-800">AI Content Generated Successfully!</h3>
                  <p class="mt-1 text-sm text-green-700">
                    The form has been pre-filled with AI-generated content. Review and edit as needed.
                  </p>
                </div>
              </div>
            </div>
          <% end %>
          
          <!-- Error Messages -->
          <% if @email_campaign.errors.any? %>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                  <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                    <% @email_campaign.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Debug Information (remove this after testing) -->
          <% if Rails.env.development? %>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <h4 class="text-sm font-medium text-yellow-800 mb-2">Debug Info:</h4>
              <div class="text-xs text-yellow-700 space-y-1">
                <div><strong>@showing_generated_content:</strong> <%= @showing_generated_content %></div>
                <div><strong>Subject Line:</strong> "<%= @email_campaign.subject_line %>"</div>
                <div><strong>Preview Text:</strong> "<%= @email_campaign.preview_text %>"</div>
                <div><strong>From Name:</strong> "<%= @email_campaign.from_name %>"</div>
                <div><strong>From Email:</strong> "<%= @email_campaign.from_email %>"</div>
                <div><strong>Content (first 100 chars):</strong> "<%= @email_campaign.content&.truncate(100) %>"</div>
              </div>
            </div>
          <% end %>

          <!-- Subject Line -->
          <div>
            <%= form.label :subject_line, "Subject Line", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :subject_line,
                class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                placeholder: "Enter your email subject line..." %>
          </div>

          <!-- Preview Text -->
          <div>
            <%= form.label :preview_text, "Preview Text", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :preview_text,
                class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                placeholder: "Preview text (optional)..." %>
          </div>

          <!-- From Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= form.label :from_name, "From Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :from_name,
                  value: @email_campaign.from_name,
                  class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                  placeholder: "Sender name" %>
            </div>
            <div>
              <%= form.label :from_email, "From Email", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.email_field :from_email,
                  value: @email_campaign.from_email,
                  class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                  placeholder: "<EMAIL>" %>
            </div>
          </div>

          <!-- Email Content -->
          <div>
            <%= form.label :content, "Email Content", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :content,
                value: @email_campaign.content,
                rows: 12,
                class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-y",
                placeholder: "Write your email content here..." %>
          </div>

          <!-- Action Buttons -->
          <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <div>
              <%= link_to campaign_path(@campaign), 
                  class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
                Cancel
              <% end %>
            </div>
            <div class="flex space-x-3">
              <%= form.submit "Save Draft", 
                  class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
              <%= form.submit "Create Email Content", 
                  class: "inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-sm" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- AI Content Generation -->
    <div class="mt-8 bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">AI Content Assistant</h3>
        <p class="text-sm text-gray-600">Generate email content using AI</p>
      </div>
      
      <div class="p-6">
        <%= form_with url: generate_ai_content_campaign_email_content_path(@campaign), local: true, class: "space-y-4" do |ai_form| %>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= ai_form.label :brand_voice, "Brand Voice", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= ai_form.select :brand_voice, 
                  options_for_select([
                    ['Professional', 'professional'],
                    ['Friendly', 'friendly'],
                    ['Casual', 'casual'],
                    ['Authoritative', 'authoritative']
                  ]),
                  { prompt: "Select brand voice..." },
                  { class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
            </div>
            <div>
              <%= ai_form.label :email_type, "Email Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= ai_form.select :email_type, 
                  options_for_select([
                    ['Promotional', 'promotional'],
                    ['Newsletter', 'newsletter'],
                    ['Welcome', 'welcome'],
                    ['Announcement', 'announcement']
                  ]),
                  { prompt: "Select email type..." },
                  { class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500" } %>
            </div>
          </div>
          
          <div>
            <%= ai_form.label :key_message, "Key Message", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= ai_form.text_area :key_message, 
                rows: 3,
                placeholder: "What's the main message you want to convey?",
                class: "w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none" %>
          </div>

          <%= ai_form.submit "Generate AI Content", 
              class: "w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-sm" %>
        <% end %>
      </div>
    </div>
  </div>
</div>
