<% content_for :title, "Create Email Content - #{@campaign.name}" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
  <!-- Enhanced <PERSON>er with Gradient -->
  <div class="bg-white/90 backdrop-blur-sm shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex items-center justify-between h-20">
        <div class="flex items-center space-x-6">
          <%= link_to campaign_path(@campaign), 
              class: "group flex items-center justify-center w-10 h-10 rounded-full bg-slate-100 hover:bg-slate-200 transition-all duration-200" do %>
            <svg class="w-5 h-5 text-slate-600 group-hover:text-slate-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
          <% end %>
          
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-3">
              <!-- Email Icon -->
              <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 shadow-lg">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
              </div>
              
              <div>
                <h1 class="text-2xl font-bold text-slate-900">Create Email Content</h1>
                <p class="text-sm text-slate-600">Design compelling emails for your campaign</p>
              </div>
            </div>
            
            <span class="inline-flex items-center px-3 py-1.5 text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 rounded-full">
              <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
              </svg>
              <%= @campaign.campaign_type.humanize %>
            </span>
          </div>
        </div>
        
        <!-- Progress Indicator -->
        <div class="hidden md:flex items-center space-x-2 text-sm text-slate-500">
          <span class="flex items-center">
            <svg class="w-4 h-4 mr-1.5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            Campaign Created
          </span>
          <svg class="w-4 h-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span class="flex items-center font-medium text-blue-600">
            <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            Email Content
          </span>
          <svg class="w-4 h-4 text-slate-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
          <span class="text-slate-400">Review & Launch</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid lg:grid-cols-3 gap-8">
      <!-- Main Form Column -->
      <div class="lg:col-span-2">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden">
          <!-- Header with Icon -->
          <div class="bg-gradient-to-r from-slate-50 to-blue-50/50 px-8 py-6">
            <div class="flex items-center space-x-4">
              <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-indigo-600 shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
              </div>
              <div>
                <h2 class="text-xl font-bold text-slate-900">Email Campaign Setup</h2>
                <p class="text-sm text-slate-600 mt-1">Create compelling email content for your campaign</p>
              </div>
            </div>
          </div>

          <div class="p-8">
            <%= form_with model: @email_campaign, url: campaign_email_content_path(@campaign), local: true, class: "space-y-8" do |form| %>
              <% if @email_campaign.errors.any? %>
                <div class="bg-red-50/80 backdrop-blur-sm rounded-xl p-6">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                      <ul class="mt-2 text-sm text-red-700 list-disc list-inside space-y-1">
                        <% @email_campaign.errors.full_messages.each do |message| %>
                          <li><%= message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              <% end %>

              <!-- Subject Line and Preview Text -->
              <div class="space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                      </svg>
                      <%= form.label :subject_line, "Subject Line", class: "block text-sm font-semibold text-slate-700" %>
                    </div>
                    <%= form.text_field :subject_line, 
                        class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-blue-500 transition-colors bg-white/50 backdrop-blur-sm",
                        placeholder: "Enter your email subject line...",
                        maxlength: 150 %>
                    <p class="text-xs text-slate-500 flex items-center">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      Maximum 150 characters
                    </p>
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                      </svg>
                      <%= form.label :preview_text, "Preview Text", class: "block text-sm font-semibold text-slate-700" %>
                    </div>
                    <%= form.text_field :preview_text, 
                        class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-indigo-500 transition-colors bg-white/50 backdrop-blur-sm",
                        placeholder: "Preview text (optional)...",
                        maxlength: 200 %>
                    <p class="text-xs text-slate-500 flex items-center">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      Maximum 200 characters
                    </p>
                  </div>
                </div>

                <!-- Sender Information -->
                <div class="bg-slate-50/50 rounded-xl p-6">
                  <div class="flex items-center space-x-2 mb-4">
                    <svg class="w-5 h-5 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <h3 class="text-lg font-semibold text-slate-900">Sender Information</h3>
                  </div>
                  
                  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        <%= form.label :from_name, "From Name", class: "block text-sm font-semibold text-slate-700" %>
                      </div>
                      <%= form.text_field :from_name, 
                          class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-emerald-500 transition-colors bg-white/70",
                          placeholder: "Sender name..." %>
                    </div>

                    <div class="space-y-2">
                      <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        <%= form.label :from_email, "From Email", class: "block text-sm font-semibold text-slate-700" %>
                      </div>
                      <%= form.email_field :from_email, 
                          class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-emerald-500 transition-colors bg-white/70",
                          placeholder: "<EMAIL>" %>
                    </div>
                  </div>
                </div>

                <!-- Email Content -->
                <div class="space-y-4">
                  <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    <%= form.label :content, "Email Content", class: "block text-lg font-semibold text-slate-900" %>
                  </div>
                  <%= form.text_area :content, 
                      class: "w-full px-4 py-4 rounded-xl focus:ring-2 focus:ring-purple-500 transition-colors resize-y bg-white/50 backdrop-blur-sm",
                      rows: 12,
                      placeholder: "Write your email content here..." %>
                  <div class="flex items-center space-x-2 text-xs text-slate-500">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                    </svg>
                    <span>You can use merge tags like {{first_name}} for personalization</span>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center justify-between pt-8">
                <div class="flex space-x-3">
                  <%= link_to campaign_path(@campaign), 
                      class: "inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors" do %>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Cancel
                  <% end %>
                </div>
                <div class="flex space-x-3">
                  <%= form.submit "Save Draft", 
                      class: "inline-flex items-center px-6 py-3 rounded-xl text-sm font-medium text-slate-700 bg-white hover:bg-slate-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors" %>
                  <%= form.submit "Create Email Content", 
                      class: "inline-flex items-center px-6 py-3 rounded-xl text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 shadow-lg transition-all duration-200" %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- AI Content Generation Sidebar -->
      <div class="lg:col-span-1">
        <div class="sticky top-8 space-y-6">
          <!-- AI Content Generation Card -->
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden">
            <!-- Card Header -->
            <div class="bg-gradient-to-r from-violet-50 to-purple-50/50 px-6 py-5">
              <div class="flex items-center space-x-3">
                <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-r from-violet-500 to-purple-600 shadow-md">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-slate-900">AI Content Generation</h3>
                  <p class="text-sm text-slate-600 mt-0.5">Let AI craft compelling emails</p>
                </div>
              </div>
            </div>

            <!-- Card Content -->
            <div class="p-6">
              <div class="mb-6">
                <div class="flex items-center space-x-2 mb-3">
                  <svg class="w-4 h-4 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                  <span class="text-sm font-medium text-slate-700">Generate personalized content based on your campaign goals</span>
                </div>
                <div class="bg-gradient-to-r from-violet-50 to-purple-50 rounded-lg p-4">
                  <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-violet-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <div class="text-sm text-violet-700">
                      <p class="font-medium mb-1">Pro Tips:</p>
                      <ul class="text-xs space-y-1 text-violet-600">
                        <li>• Be specific about your target audience</li>
                        <li>• Include your key value proposition</li>
                        <li>• Mention any special offers or deadlines</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <%= form_with url: generate_ai_content_campaign_email_content_path(@campaign),
                  method: :post, local: true, class: "space-y-5" do |form| %>
                
                <div class="grid grid-cols-1 gap-5">
                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                      </svg>
                      <%= form.label :brand_voice, "Brand Voice", class: "block text-sm font-semibold text-slate-700" %>
                    </div>
                    <%= form.select :brand_voice, 
                        options_for_select([
                          ['Professional', 'professional'],
                          ['Friendly', 'friendly'],
                          ['Casual', 'casual'],
                          ['Authoritative', 'authoritative'],
                          ['Playful', 'playful']
                        ], 'professional'),
                        {}, { 
                          class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-blue-500 transition-colors bg-white/70 text-slate-900"
                        } %>
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                      </svg>
                      <%= form.label :email_type, "Email Type", class: "block text-sm font-semibold text-slate-700" %>
                    </div>
                    <%= form.select :email_type,
                        options_for_select([
                          ['Promotional', 'promotional'],
                          ['Newsletter', 'newsletter'],
                          ['Welcome', 'welcome'],
                          ['Follow-up', 'followup'],
                          ['Announcement', 'announcement']
                        ], 'promotional'),
                        {}, { 
                          class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-emerald-500 transition-colors bg-white/70 text-slate-900"
                        } %>
                  </div>

                  <div class="space-y-2">
                    <div class="flex items-center space-x-2">
                      <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                      </svg>
                      <%= form.label :key_message, "Key Message", class: "block text-sm font-semibold text-slate-700" %>
                    </div>
                    <%= form.text_area :key_message, 
                        placeholder: "What's the main message you want to convey? Include your value proposition, target audience, and any special offers...",
                        class: "w-full px-4 py-3 rounded-xl focus:ring-2 focus:ring-purple-500 transition-colors resize-y bg-white/70",
                        rows: 4 %>
                  </div>
                </div>

                <%= form.submit "Generate AI Content", 
                    class: "w-full inline-flex items-center justify-center px-6 py-3 rounded-xl text-sm font-medium text-white bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 shadow-lg transition-all duration-200",
                    data: { turbo_confirm: "This will populate the form with AI-generated content. Continue?" } %>
              <% end %>
            </div>
          </div>

          <!-- Email Preview Card -->
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-amber-50 to-orange-50/50 px-6 py-5">
              <div class="flex items-center space-x-3">
                <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-r from-amber-500 to-orange-600 shadow-md">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-slate-900">Email Preview</h3>
                  <p class="text-sm text-slate-600 mt-0.5">See how your email will look</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="bg-slate-50 rounded-xl p-4">
                <div class="space-y-3">
                  <div class="flex items-center space-x-2 text-sm">
                    <span class="font-medium text-slate-600">From:</span>
                    <span class="text-slate-800" id="preview-from">Your Name &lt;<EMAIL>&gt;</span>
                  </div>
                  <div class="flex items-center space-x-2 text-sm">
                    <span class="font-medium text-slate-600">Subject:</span>
                    <span class="text-slate-800 font-medium" id="preview-subject">Your email subject line...</span>
                  </div>
                  <div class="bg-white rounded-lg p-4 min-h-[120px] email-preview">
                    <div class="text-sm text-slate-600" id="preview-content">
                      Start typing your email content to see a preview here...
                    </div>
                  </div>
                  <div class="flex items-center justify-between text-xs text-slate-500">
                    <span class="flex items-center">
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                      Live Preview
                    </span>
                    <span class="flex items-center">
                      <div class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                      Updated in real-time
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Email Best Practices Card -->
          <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden">
            <div class="bg-gradient-to-r from-emerald-50 to-teal-50/50 px-6 py-5">
              <div class="flex items-center space-x-3">
                <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-r from-emerald-500 to-teal-600 shadow-md">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <div>
                  <h3 class="text-lg font-bold text-slate-900">Best Practices</h3>
                  <p class="text-sm text-slate-600 mt-0.5">Tips for effective emails</p>
                </div>
              </div>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
                    <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-slate-900">Subject Line</h4>
                    <p class="text-xs text-slate-600 mt-1">Keep it under 50 characters for mobile optimization. Create urgency without being spammy.</p>
                  </div>
                </div>
                
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mt-0.5">
                    <svg class="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-slate-900">Personalization</h4>
                    <p class="text-xs text-slate-600 mt-1">Use merge tags like {{first_name}} and {{company}} to personalize content.</p>
                  </div>
                </div>
                
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                    <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 class="text-sm font-medium text-slate-900">Call-to-Action</h4>
                    <p class="text-xs text-slate-600 mt-1">Include a clear, single call-to-action button. Use action-oriented text.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
