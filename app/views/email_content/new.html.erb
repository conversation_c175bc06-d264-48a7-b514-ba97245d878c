<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Email Content - Campaign Name</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'gradient': 'gradient 8s ease infinite',
                        'pulse-slow': 'pulse 3s ease-in-out infinite',
                        'bounce-gentle': 'bounce 2s ease-in-out infinite',
                    },
                    keyframes: {
                        gradient: {
                            '0%, 100%': {
                                'background-size': '200% 200%',
                                'background-position': 'left center'
                            },
                            '50%': {
                                'background-size': '200% 200%',
                                'background-position': 'right center'
                            }
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">

<!-- Floating Navigation Header -->
<div class="sticky top-0 z-50 backdrop-blur-2xl bg-white/80 shadow-xl shadow-black/5">
    <div class="max-w-8xl mx-auto px-8 py-6">
        <div class="flex items-center justify-between">
            <!-- Left Section -->
            <div class="flex items-center space-x-6">
                <button class="group flex items-center justify-center w-12 h-12 rounded-2xl bg-gradient-to-br from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 text-gray-600 group-hover:text-gray-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>

                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <div class="flex items-center justify-center w-16 h-16 rounded-3xl bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 shadow-2xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div class="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full flex items-center justify-center shadow-lg">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent">Create Email Content</h1>
                        <p class="text-gray-600 font-medium flex items-center space-x-2">
                            <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                            </svg>
                            <span>Summer Marketing Campaign 2024</span>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Right Section -->
            <div class="flex items-center space-x-4">
                <!-- AI Status Indicator -->
                <div class="flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-emerald-50 to-green-50 rounded-2xl shadow-sm hover:shadow-md transition-all">
                    <div class="relative">
                        <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full shadow-sm"></div>
                        <div class="absolute inset-0 w-3 h-3 bg-emerald-400 rounded-full animate-ping opacity-30"></div>
                    </div>
                    <span class="text-sm font-semibold text-emerald-700">3 AI providers ready</span>
                </div>

                <!-- Auto-save Status -->
                <div class="flex items-center space-x-3 px-4 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl shadow-sm hover:shadow-md transition-all">
                    <svg class="w-4 h-4 text-blue-600 animate-pulse-slow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    <span class="text-sm font-semibold text-blue-700">Auto-save active</span>
                </div>

                <!-- Settings Button -->
                <button class="flex items-center justify-center w-12 h-12 rounded-2xl bg-white hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                    </svg>
                </button>

                <!-- Cancel Button -->
                <button class="inline-flex items-center px-6 py-3 rounded-2xl text-sm font-semibold text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<div class="max-w-8xl mx-auto px-8 py-12">
    <div class="grid lg:grid-cols-12 gap-8">
        
        <!-- Main Form Section -->
        <div class="lg:col-span-8">
            <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl shadow-black/10 overflow-hidden">
                
                <!-- Enhanced Form Header -->
                <div class="relative bg-gradient-to-r from-violet-50 via-blue-50 to-indigo-50 px-8 py-8 overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-r from-violet-100/30 via-blue-100/30 to-indigo-100/30 animate-gradient"></div>
                    <div class="relative flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <div class="flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-br from-violet-500 via-indigo-600 to-purple-700 shadow-xl">
                                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                    </svg>
                                </div>
                                <div class="absolute -top-1 -right-1 w-6 h-6 bg-gradient-to-r from-pink-400 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-bounce-gentle">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold bg-gradient-to-r from-gray-900 via-violet-900 to-indigo-900 bg-clip-text text-transparent">Email Campaign Setup</h2>
                                <p class="text-gray-600 font-medium">Craft compelling email content that converts and engages</p>
                            </div>
                        </div>
                        
                        <!-- Progress Indicator -->
                        <div class="flex items-center space-x-2">
                            <div class="flex items-center space-x-1">
                                <div class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-gray-300 rounded-full"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-600">Step 2 of 3</span>
                        </div>
                    </div>
                </div>

                <div class="p-8">
                    <form class="space-y-8">
                        
                        <!-- Email Basics Section -->
                        <div class="space-y-8">
                            
                            <!-- Subject Line & Preview Grid -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                
                                <!-- Subject Line -->
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-100 via-blue-200 to-indigo-200 shadow-sm">
                                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <label class="text-lg font-bold text-gray-900">Subject Line</label>
                                            <p class="text-sm text-gray-500">The first impression that drives opens</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <input type="text" 
                                               class="w-full px-6 py-4 rounded-2xl bg-gradient-to-r from-blue-50 to-indigo-50 focus:from-blue-100 focus:to-indigo-100 focus:ring-4 focus:ring-blue-200/50 transition-all duration-300 text-gray-900 placeholder-gray-500 text-lg font-medium shadow-sm group-hover:shadow-md"
                                               placeholder="🚀 Don't miss out! Limited time offer inside..."
                                               maxlength="150">
                                        <div class="absolute inset-y-0 right-4 flex items-center space-x-2">
                                            <span class="text-xs font-medium text-blue-600 bg-white px-2 py-1 rounded-lg shadow-sm">
                                                0/150
                                            </span>
                                            <div class="w-6 h-6 rounded-lg bg-blue-100 flex items-center justify-center">
                                                <svg class="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Subject Line Tips -->
                                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <span class="text-sm font-semibold text-blue-800">Pro Tips</span>
                                        </div>
                                        <ul class="text-xs text-blue-700 space-y-1">
                                            <li class="flex items-center space-x-2">
                                                <div class="w-1 h-1 bg-blue-400 rounded-full"></div>
                                                <span>Keep under 50 characters for mobile</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="w-1 h-1 bg-blue-400 rounded-full"></div>
                                                <span>Use action words and urgency</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="w-1 h-1 bg-blue-400 rounded-full"></div>
                                                <span>Include personalization when possible</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Preview Text -->
                                <div class="space-y-4">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-purple-100 via-purple-200 to-pink-200 shadow-sm">
                                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <label class="text-lg font-bold text-gray-900">Preview Text</label>
                                            <p class="text-sm text-gray-500">Appears next to subject in inbox</p>
                                        </div>
                                    </div>
                                    
                                    <div class="relative group">
                                        <input type="text" 
                                               class="w-full px-6 py-4 rounded-2xl bg-gradient-to-r from-purple-50 to-pink-50 focus:from-purple-100 focus:to-pink-100 focus:ring-4 focus:ring-purple-200/50 transition-all duration-300 text-gray-900 placeholder-gray-500 text-lg font-medium shadow-sm group-hover:shadow-md"
                                               placeholder="Get 30% off all premium plans - expires soon!"
                                               maxlength="200">
                                        <div class="absolute inset-y-0 right-4 flex items-center space-x-2">
                                            <span class="text-xs font-medium text-purple-600 bg-white px-2 py-1 rounded-lg shadow-sm">
                                                0/200
                                            </span>
                                            <div class="w-6 h-6 rounded-lg bg-purple-100 flex items-center justify-center">
                                                <svg class="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Preview Text Tips -->
                                    <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-4 space-y-2">
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                            </svg>
                                            <span class="text-sm font-semibold text-purple-800">Optimization Tips</span>
                                        </div>
                                        <ul class="text-xs text-purple-700 space-y-1">
                                            <li class="flex items-center space-x-2">
                                                <div class="w-1 h-1 bg-purple-400 rounded-full"></div>
                                                <span>Complement, don't repeat the subject</span>
                                            </li>
                                            <li class="flex items-center space-x-2">
                                                <div class="w-1 h-1 bg-purple-400 rounded-full"></div>
                                                <span>Include your main value proposition</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Sender Information -->
                            <div class="relative bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 rounded-3xl p-8 shadow-sm overflow-hidden">
                                <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-200/30 to-green-300/30 rounded-full -translate-y-16 translate-x-16"></div>
                                <div class="relative">
                                    <div class="flex items-center justify-between mb-6">
                                        <div class="flex items-center space-x-4">
                                            <div class="flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-br from-emerald-500 via-green-600 to-teal-600 shadow-xl">
                                                <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-bold text-gray-900">Sender Information</h3>
                                                <p class="text-gray-600">Establish trust with your email identity</p>
                                            </div>
                                        </div>
                                        
                                        <!-- Verification Badge -->
                                        <div class="flex items-center space-x-2 px-3 py-1.5 bg-white/80 rounded-xl shadow-sm">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                            </svg>
                                            <span class="text-sm font-medium text-green-700">Verified Domain</span>
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        <div class="space-y-3">
                                            <div class="flex items-center space-x-3">
                                                <div class="flex items-center justify-center w-8 h-8 rounded-xl bg-white/80 shadow-sm">
                                                    <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                    </svg>
                                                </div>
                                                <label class="text-base font-semibold text-gray-800">From Name</label>
                                            </div>
                                            <input type="text" 
                                                   class="w-full px-5 py-4 rounded-2xl bg-white/80 focus:bg-white focus:ring-4 focus:ring-emerald-200/50 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                                   placeholder="Your Company Name"
                                                   value="TechStart Solutions">
                                        </div>

                                        <div class="space-y-3">
                                            <div class="flex items-center space-x-3">
                                                <div class="flex items-center justify-center w-8 h-8 rounded-xl bg-white/80 shadow-sm">
                                                    <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <label class="text-base font-semibold text-gray-800">From Email</label>
                                            </div>
                                            <input type="email" 
                                                   class="w-full px-5 py-4 rounded-2xl bg-white/80 focus:bg-white focus:ring-4 focus:ring-emerald-200/50 transition-all duration-200 text-gray-900 font-medium shadow-sm hover:shadow-md"
                                                   placeholder="<EMAIL>"
                                                   value="<EMAIL>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Email Content Section -->
                            <div class="space-y-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-500 via-purple-600 to-pink-600 shadow-xl">
                                            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <h3 class="text-xl font-bold text-gray-900">Email Content</h3>
                                            <p class="text-gray-600">Craft your compelling message</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Content Tools -->
                                    <div class="flex items-center space-x-2">
                                        <button type="button" class="flex items-center space-x-2 px-4 py-2 rounded-xl bg-white shadow-sm hover:shadow-md transition-all">
                                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                            </svg>
                                            <span class="text-sm font-medium text-gray-700">Add Image</span>
                                        </button>
                                        <button type="button" class="flex items-center space-x-2 px-4 py-2 rounded-xl bg-white shadow-sm hover:shadow-md transition-all">
                                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                            </svg>
                                            <span class="text-sm font-medium text-gray-700">Add Link</span>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="relative">
                                    <textarea class="w-full px-6 py-6 rounded-3xl bg-gradient-to-br from-slate-50 to-gray-50 focus:from-white focus:to-slate-50 focus:ring-4 focus:ring-indigo-200/50 transition-all duration-300 resize-y text-gray-900 placeholder-gray-500 text-base leading-relaxed shadow-lg hover:shadow-xl"
                                              rows="16"
                                              placeholder="Hi {{first_name}},

I hope this email finds you well! I'm excited to share something special with you...

🎉 For a limited time, we're offering an exclusive 30% discount on all our premium plans. This is perfect timing if you've been considering upgrading your account.

Here's what you'll get with our premium features:
• Advanced analytics and reporting
• Priority customer support
• Unlimited integrations
• Custom branding options

This offer expires in 48 hours, so don't wait too long!

Ready to take advantage of this deal? Click the button below:

[Claim Your 30% Discount]

Questions? Just reply to this email - I'm here to help!

Best regards,
Sarah from TechStart
P.S. This discount won't last long - grab it while you can! 🚀"></textarea>
                                    
                                    <!-- Word Count and Tools -->
                                    <div class="absolute bottom-4 right-4 flex items-center space-x-3">
                                        <div class="flex items-center space-x-2 px-3 py-1.5 bg-white/90 rounded-xl shadow-sm">
                                            <svg class="w-3 h-3 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                            </svg>
                                            <span class="text-xs font-medium text-indigo-700">892 words</span>
                                        </div>
                                        <div class="flex items-center space-x-2 px-3 py-1.5 bg-white/90 rounded-xl shadow-sm">
                                            <svg class="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            <span class="text-xs font-medium text-green-700">3 min read</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Enhanced Personalization Guide -->
                                <div class="bg-gradient-to-r from-amber-50 via-yellow-50 to-orange-50 rounded-2xl p-6 shadow-sm">
                                    <div class="flex items-start space-x-4">
                                        <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-amber-100 to-orange-200 shadow-sm">
                                            <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="flex items-center justify-between mb-3">
                                                <h4 class="font-semibold text-amber-800">Personalization Tags Available</h4>
                                                <button type="button" class="text-xs font-medium text-amber-700 hover:text-amber-800 underline">View All Tags</button>
                                            </div>
                                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
                                                <div class="flex items-center space-x-2 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer">
                                                    <code class="text-xs font-mono text-amber-700">{{first_name}}</code>
                                                    <svg class="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <div class="flex items-center space-x-2 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer">
                                                    <code class="text-xs font-mono text-amber-700">{{company}}</code>
                                                    <svg class="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <div class="flex items-center space-x-2 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer">
                                                    <code class="text-xs font-mono text-amber-700">{{industry}}</code>
                                                    <svg class="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <div class="flex items-center space-x-2 px-3 py-2 bg-white rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer">
                                                    <code class="text-xs font-mono text-amber-700">{{location}}</code>
                                                    <svg class="w-3 h-3 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Action Buttons -->
                        <div class="flex items-center justify-between pt-8">
                            <div class="flex items-center space-x-4">
                                <button type="button" class="inline-flex items-center px-8 py-4 rounded-2xl text-base font-semibold text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                    Cancel
                                </button>
                                
                                <button type="button" class="inline-flex items-center px-8 py-4 rounded-2xl text-base font-semibold text-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"/>
                                    </svg>
                                    Save Draft
                                </button>
                            </div>
                            
                            <div class="flex items-center space-x-4">
                                <button type="button" class="inline-flex items-center px-8 py-4 rounded-2xl text-base font-semibold text-white bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 hover:from-emerald-700 hover:via-green-700 hover:to-teal-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-0.5">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                    Preview Email
                                </button>
                                
                                <button type="submit" class="inline-flex items-center px-8 py-4 rounded-2xl text-base font-semibold text-white bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-0.5">
                                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                    </svg>
                                    Create Email Content
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Enhanced AI Assistant Sidebar -->
        <div class="lg:col-span-4">
            <div class="sticky top-28 space-y-6">
                
                <!-- AI Content Generation -->
                <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl shadow-black/10 overflow-hidden">
                    <div class="relative bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 px-8 py-8 overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-br from-violet-100/40 via-purple-100/40 to-fuchsia-100/40 animate-gradient"></div>
                        <div class="relative flex items-center space-x-4">
                            <div class="relative">
                                <div class="flex items-center justify-center w-16 h-16 rounded-3xl bg-gradient-to-br from-violet-500 via-purple-600 to-fuchsia-600 shadow-2xl">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                    </svg>
                                </div>
                                <div class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-pink-400 to-rose-500 rounded-full flex items-center justify-center shadow-xl animate-pulse">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold bg-gradient-to-r from-violet-800 via-purple-800 to-fuchsia-800 bg-clip-text text-transparent">AI Content Magic</h3>
                                <p class="text-gray-600 font-medium">Generate compelling content instantly</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-8">
                        <!-- AI Tips -->
                        <div class="mb-8">
                            <div class="bg-gradient-to-r from-violet-100 via-purple-100 to-fuchsia-100 rounded-2xl p-6 shadow-sm">
                                <div class="flex items-start space-x-4">
                                    <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-white to-violet-50 shadow-lg">
                                        <svg class="w-6 h-6 text-violet-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-bold text-violet-800 mb-3 flex items-center space-x-2">
                                            <span>🎯 AI-Powered Tips</span>
                                            <div class="px-2 py-1 bg-violet-200 rounded-lg text-xs font-semibold">GPT-4</div>
                                        </h4>
                                        <ul class="space-y-2 text-violet-700">
                                            <li class="flex items-start space-x-3">
                                                <div class="w-2 h-2 bg-gradient-to-r from-violet-400 to-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                                                <span class="text-sm">Be specific about your target audience and their pain points</span>
                                            </li>
                                            <li class="flex items-start space-x-3">
                                                <div class="w-2 h-2 bg-gradient-to-r from-violet-400 to-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                                                <span class="text-sm">Include your unique value proposition and benefits</span>
                                            </li>
                                            <li class="flex items-start space-x-3">
                                                <div class="w-2 h-2 bg-gradient-to-r from-violet-400 to-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                                                <span class="text-sm">Mention any urgency, deadlines, or limited offers</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Form -->
                        <form class="space-y-6">
                            <div class="space-y-6">
                                <!-- Brand Voice -->
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-xl bg-gradient-to-br from-blue-100 to-blue-200">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                                            </svg>
                                        </div>
                                        <label class="text-base font-bold text-gray-900">Brand Voice & Tone</label>
                                    </div>
                                    <select class="w-full px-5 py-4 rounded-2xl bg-gradient-to-r from-blue-50 to-indigo-50 focus:from-blue-100 focus:to-indigo-100 focus:ring-4 focus:ring-blue-200/50 transition-all duration-200 text-gray-900 font-medium shadow-sm">
                                        <option>Professional & Trustworthy</option>
                                        <option>Friendly & Approachable</option>
                                        <option>Casual & Conversational</option>
                                        <option>Authoritative & Expert</option>
                                        <option>Playful & Creative</option>
                                        <option>Urgent & Action-Oriented</option>
                                    </select>
                                </div>

                                <!-- Email Type -->
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-100 to-emerald-200">
                                            <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                            </svg>
                                        </div>
                                        <label class="text-base font-bold text-gray-900">Email Campaign Type</label>
                                    </div>
                                    <select class="w-full px-5 py-4 rounded-2xl bg-gradient-to-r from-emerald-50 to-green-50 focus:from-emerald-100 focus:to-green-100 focus:ring-4 focus:ring-emerald-200/50 transition-all duration-200 text-gray-900 font-medium shadow-sm">
                                        <option>🎯 Promotional Campaign</option>
                                        <option>📰 Newsletter Update</option>
                                        <option>👋 Welcome Series</option>
                                        <option>🔄 Follow-up Sequence</option>
                                        <option>📢 Product Announcement</option>
                                        <option>🎁 Special Offer</option>
                                        <option>📈 Educational Content</option>
                                    </select>
                                </div>

                                <!-- Target Audience -->
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-xl bg-gradient-to-br from-orange-100 to-orange-200">
                                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                        </div>
                                        <label class="text-base font-bold text-gray-900">Target Audience</label>
                                    </div>
                                    <input type="text" 
                                           class="w-full px-5 py-4 rounded-2xl bg-gradient-to-r from-orange-50 to-amber-50 focus:from-orange-100 focus:to-amber-100 focus:ring-4 focus:ring-orange-200/50 transition-all duration-200 text-gray-900 font-medium shadow-sm"
                                           placeholder="e.g., Small business owners, SaaS managers, Marketing professionals..."
                                           value="Small and medium business owners">
                                </div>

                                <!-- Key Message -->
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="flex items-center justify-center w-8 h-8 rounded-xl bg-gradient-to-br from-purple-100 to-purple-200">
                                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </div>
                                        <label class="text-base font-bold text-gray-900">Key Message & Goals</label>
                                    </div>
                                    <textarea class="w-full px-5 py-4 rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 focus:from-purple-100 focus:to-pink-100 focus:ring-4 focus:ring-purple-200/50 transition-all duration-200 resize-y text-gray-900 font-medium shadow-sm"
                                              rows="6"
                                              placeholder="Describe your main message, value proposition, special offers, deadlines, and the action you want recipients to take...">We're offering a limited-time 30% discount on all premium plans. This is perfect for businesses looking to scale their operations with advanced features. The offer expires in 48 hours and includes priority support, advanced analytics, and custom integrations.</textarea>
                                </div>
                            </div>

                            <!-- Generate Button -->
                            <button type="submit" class="w-full inline-flex items-center justify-center px-8 py-4 rounded-2xl text-base font-bold text-white bg-gradient-to-r from-violet-600 via-purple-600 to-fuchsia-600 hover:from-violet-700 hover:via-purple-700 hover:to-fuchsia-700 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1">
                                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                </svg>
                                ✨ Generate AI Content
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Live Email Preview -->
                <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl shadow-black/10 overflow-hidden">
                    <div class="bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 px-8 py-8">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center justify-center w-16 h-16 rounded-3xl bg-gradient-to-br from-amber-500 via-orange-600 to-red-600 shadow-2xl">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Live Preview</h3>
                                <p class="text-gray-600">See your email in real-time</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-8">
                        <!-- Email Client Mockup -->
                        <div class="bg-gradient-to-br from-gray-50 to-slate-50 rounded-2xl shadow-lg overflow-hidden">
                            <!-- Email Header -->
                            <div class="bg-white px-6 py-4 shadow-sm">
                                <div class="flex items-center justify-between text-sm">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                            <span class="text-white font-bold text-xs">TS</span>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">TechStart Solutions</div>
                                            <div class="text-gray-500 text-xs"><EMAIL></div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                                            <svg class="w-3 h-3 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        </div>
                                        <span class="text-xs text-gray-500">2:34 PM</span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="font-semibold text-gray-900 text-base">🚀 Don't miss out! Limited time offer inside...</div>
                                    <div class="text-sm text-gray-600 mt-1">Get 30% off all premium plans - expires soon!</div>
                                </div>
                            </div>
                            
                            <!-- Email Content Preview -->
                            <div class="bg-white p-6 min-h-[300px]">
                                <div class="text-sm text-gray-700 leading-relaxed space-y-4">
                                    <p><strong>Hi John,</strong></p>
                                    <p>I hope this email finds you well! I'm excited to share something special with you...</p>
                                    <p>🎉 For a limited time, we're offering an exclusive <strong>30% discount</strong> on all our premium plans.</p>
                                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 my-4">
                                        <p class="font-semibold text-blue-800">Here's what you'll get:</p>
                                        <ul class="text-blue-700 mt-2 space-y-1">
                                            <li>• Advanced analytics and reporting</li>
                                            <li>• Priority customer support</li>
                                            <li>• Unlimited integrations</li>
                                        </ul>
                                    </div>
                                    <div class="text-center py-4">
                                        <button class="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all">
                                            Claim Your 30% Discount
                                        </button>
                                    </div>
                                    <p class="text-sm">Best regards,<br>Sarah from TechStart</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Preview Stats -->
                        <div class="flex items-center justify-between mt-6 text-xs text-gray-500">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span class="font-medium">Live Preview Active</span>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-1">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>Updates instantly</span>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                    </svg>
                                    <span>85% mobile ready</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Best Practices -->
                <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl shadow-black/10 overflow-hidden">
                    <div class="bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 px-8 py-8">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center justify-center w-16 h-16 rounded-3xl bg-gradient-to-br from-emerald-500 via-teal-600 to-cyan-600 shadow-2xl">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Best Practices</h3>
                                <p class="text-gray-600">Expert tips for email success</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-8">
                        <div class="space-y-6">
                            <div class="flex items-start space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl">
                                <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-base font-bold text-gray-900 mb-2">Subject Line Mastery</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Keep it under 50 characters for mobile optimization. Create urgency without being spammy. Use action words, numbers, and personalization for higher open rates.</p>
                                    <div class="mt-2 flex items-center space-x-2">
                                        <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-lg text-xs font-medium">22% higher opens</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-4 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl">
                                <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 shadow-lg flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-base font-bold text-gray-900 mb-2">Smart Personalization</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Go beyond names. Use company data, location, past behavior, and industry-specific content. Dynamic content increases engagement significantly.</p>
                                    <div class="mt-2 flex items-center space-x-2">
                                        <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-lg text-xs font-medium">45% more clicks</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-start space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl">
                                <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-green-500 to-emerald-600 shadow-lg flex-shrink-0">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7"/>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="text-base font-bold text-gray-900 mb-2">CTA Excellence</h4>
                                    <p class="text-sm text-gray-600 leading-relaxed">Use one clear, action-oriented CTA. Make it prominent with contrasting colors. Test different copy and placement for optimal conversion rates.</p>
                                    <div class="mt-2 flex items-center space-x-2">
                                        <span class="px-2 py-1 bg-green-100 text-green-700 rounded-lg text-xs font-medium">38% more conversions</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Simple interactivity for demo purposes
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save simulation
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('input', function() {
            // Simulate auto-save
            console.log('Auto-saving...');
        });
    });
    
    // Character counter for subject line
    const subjectInput = document.querySelector('input[placeholder*="subject"]');
    if (subjectInput) {
        const counter = subjectInput.parentElement.querySelector('span');
        subjectInput.addEventListener('input', function() {
            if (counter) {
                counter.textContent = `${this.value.length}/150`;
            }
        });
    }
    
    // Live preview updates
    const contentTextarea = document.querySelector('textarea[rows="16"]');
    if (contentTextarea) {
        contentTextarea.addEventListener('input', function() {
            // Update preview content
            console.log('Updating preview...');
        });
    }
});
</script>

</body>
</html>