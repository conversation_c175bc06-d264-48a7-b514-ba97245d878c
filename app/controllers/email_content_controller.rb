# frozen_string_literal: true

# Email Content Management Controller
# Handles email campaign content creation, editing, and management
class EmailContentController < ApplicationController
  include AiProvidersHelper

  before_action :authenticate_user!
  before_action :set_current_tenant
  before_action :set_campaign
  before_action :set_email_campaign, only: [:show, :edit, :update, :destroy, :preview, :optimize_content]

  # GET /campaigns/:campaign_id/email_content
  def show
    @email_campaign = @campaign.email_campaign || @campaign.build_email_campaign(default_email_attributes)
    @preview_data = {
      subject_line: @email_campaign.subject_line,
      preview_text: @email_campaign.preview_text,
      content: @email_campaign.content,
      from_name: @email_campaign.from_name,
      from_email: @email_campaign.from_email
    }
  end

  # GET /campaigns/:campaign_id/email_content/new
  def new
    Rails.logger.info "🔍 Session content: #{session[:generated_email_content].inspect}"

    # Check if we have generated content in session first (takes priority)
    if session[:generated_email_content].present?
      Rails.logger.info "🎯 Loading generated content from session for review"
      @email_campaign = @campaign.build_email_campaign(session[:generated_email_content])
      @showing_generated_content = true
      Rails.logger.info "✅ Email campaign built with: #{@email_campaign.attributes.inspect}"
    elsif @campaign.email_campaign.present?
      # Only redirect to show if no generated content is waiting for review
      Rails.logger.info "📧 Email campaign exists, redirecting to show page"
      redirect_to campaign_email_content_path(@campaign)
      return
    else
      # Create new email campaign with defaults
      Rails.logger.info "📝 Creating new email campaign with defaults"
      @email_campaign = @campaign.build_email_campaign(default_email_attributes)
      @showing_generated_content = false
    end
  end

  # GET /campaigns/:campaign_id/email_content/edit
  def edit
    @email_campaign = @campaign.email_campaign
    redirect_to new_campaign_email_content_path(@campaign) unless @email_campaign
  end

  # POST /campaigns/:campaign_id/email_content
  def create
    @email_campaign = @campaign.build_email_campaign(email_campaign_params)

    if @email_campaign.save
      # Clear the generated content session after successful save
      session.delete(:generated_email_content)
      flash[:notice] = 'Email content created successfully!'
      redirect_to campaign_email_content_path(@campaign)
    else
      # Keep the generated content in session for retry
      @showing_generated_content = session[:generated_email_content].present?
      flash.now[:alert] = 'Failed to create email content.'
      render :new, status: :unprocessable_entity
    end
  end

  # PATCH/PUT /campaigns/:campaign_id/email_content
  def update
    @email_campaign = @campaign.email_campaign
    if @email_campaign&.update(email_campaign_params)
      flash[:notice] = 'Email content updated successfully!'
      redirect_to campaign_email_content_path(@campaign)
    else
      flash.now[:alert] = 'Failed to update email content.'
      render :edit, status: :unprocessable_entity
    end
  end

  # DELETE /campaigns/:campaign_id/email_content
  def destroy
    @email_campaign = @campaign.email_campaign
    @email_campaign&.destroy
    flash[:notice] = 'Email content deleted successfully!'
    redirect_to campaign_path(@campaign)
  end

  # GET /campaigns/:campaign_id/email_content/:id/preview
  def preview
    render json: {
      subject_line: @email_campaign.subject_line,
      preview_text: @email_campaign.preview_text,
      content: @email_campaign.content,
      from_name: @email_campaign.from_name,
      from_email: @email_campaign.from_email,
      estimated_send_time: @email_campaign.estimated_send_time
    }
  end

  # POST /campaigns/:campaign_id/email_content/:id/generate_ai_content
  def generate_ai_content
    Rails.logger.info "🤖 Starting AI content generation for campaign: #{@campaign.name}"

    service = EmailSpecialistAgentService.new(campaign: @campaign)
    result = service.generate_campaign_content(
      brand_voice: params[:brand_voice] || 'professional',
      email_type: params[:email_type] || 'promotional',
      key_message: params[:key_message]
    )

    Rails.logger.info "📊 AI service result: #{result.inspect}"

    if result[:status] == 'success'
      # Extract the generated content from campaign_data (not :content)
      generated_content = result[:campaign_data]

      Rails.logger.info "📝 Generated content structure: #{generated_content.inspect}"

      if generated_content.present?
        # Store generated content in session to pre-fill the form
        session_content = {
          subject_line: generated_content[:subject_line] || "AI Generated Subject",
          preview_text: generated_content[:preview_text] || "AI Generated Preview",
          content: generated_content[:content] || "AI Generated Content",
          from_name: generated_content[:from_name] || current_user.full_name || current_tenant.name,
          from_email: generated_content[:from_email] || current_user.email
        }

        session[:generated_email_content] = session_content
        Rails.logger.info "✅ Content stored in session: #{session_content.inspect}"
        Rails.logger.info "🔍 Session after storage: #{session[:generated_email_content].inspect}"

        flash[:notice] = "🎉 AI email content generated successfully! Review the content below and make any adjustments before saving."
        redirect_to new_campaign_email_content_path(@campaign)
      else
        Rails.logger.error "❌ Generated content is nil or empty"
        flash[:alert] = "AI content was generated but could not be extracted. Please try again."
        redirect_to new_campaign_email_content_path(@campaign)
      end
    else
      Rails.logger.error "❌ AI generation failed: #{result[:message]}"
      flash[:alert] = "Failed to generate AI content: #{result[:message] || 'Unknown error'}"
      redirect_to new_campaign_email_content_path(@campaign)
    end
  rescue => error
    Rails.logger.error "💥 Exception in generate_ai_content: #{error.message}"
    Rails.logger.error "📍 Backtrace: #{error.backtrace.first(5).join('\n')}"
    flash[:alert] = "An error occurred while generating AI content. Please try again."
    redirect_to new_campaign_email_content_path(@campaign)
  end

  # POST /campaigns/:campaign_id/email_content/:id/optimize_content
  def optimize_content
    service = EmailSpecialistAgentService.new(campaign: @campaign)
    result = service.optimize_campaign_content(
      optimization_goals: params[:optimization_goals]&.split(',')&.map(&:strip) || ['engagement'],
      target_metrics: params[:target_metrics]
    )

    if result[:status] == 'success'
      flash[:notice] = 'Email content optimized successfully!'
    else
      flash[:alert] = "Failed to optimize content: #{result[:message]}"
    end

    redirect_to campaign_email_content_path(@campaign)
  end

  private

  def set_current_tenant
    ActsAsTenant.current_tenant = current_user.tenant
  end

  def current_tenant
    current_user.tenant
  end

  def set_campaign
    @campaign = current_tenant.campaigns.find(params[:campaign_id])
  end

  def set_email_campaign
    @email_campaign = @campaign.email_campaign
    redirect_to new_campaign_email_content_path(@campaign) unless @email_campaign
  end

  def email_campaign_params
    params.require(:email_campaign).permit(
      :subject_line, :preview_text, :content, :from_name, :from_email,
      settings: {}
    )
  end

  def default_email_attributes
    {
      subject_line: "#{@campaign.name} - Email Campaign",
      content: "Email content for #{@campaign.name}",
      from_name: current_user.full_name || current_tenant.name,
      from_email: current_user.email
    }
  end
end
