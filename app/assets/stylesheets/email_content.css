/* Email Content Page Enhancements */

/* Gradient animation for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Loading skeleton for AI generation */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced form field styling */
.form-field-enhanced {
  transition: all 0.2s ease-in-out;
}

.form-field-enhanced:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Glassmorphism effect for cards */
.glass-card {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hover effects for interactive elements */
.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Progress indicator animation */
.progress-indicator {
  position: relative;
  overflow: hidden;
}

.progress-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Character counter styling */
.character-counter {
  font-variant-numeric: tabular-nums;
  transition: color 0.2s ease-in-out;
}

/* Email preview styling */
.email-preview {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.email-preview p {
  margin-bottom: 1em;
}

.email-preview a {
  color: #3b82f6;
  text-decoration: underline;
}

/* AI generation form enhancements */
.ai-form-card {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
}

/* Responsive grid adjustments */
@media (max-width: 1024px) {
  .lg\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
  
  .lg\:col-span-2 {
    grid-column: span 1;
  }
  
  .lg\:col-span-1 {
    grid-column: span 1;
  }
}

/* Focus indicators for accessibility */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Button hover states */
.btn-gradient {
  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
  transition: all 0.2s ease-in-out;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #2563eb 0%, #4f46e5 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Input field enhancements */
.input-enhanced {
  transition: all 0.2s ease-in-out;
  background: rgba(255, 255, 255, 0.8);
}

.input-enhanced:focus {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

/* Notification styles for success/error states */
.notification-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.notification-error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  padding: 1rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}
