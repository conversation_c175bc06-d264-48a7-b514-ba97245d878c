// Real-time email preview functionality
document.addEventListener('DOMContentLoaded', function() {
  const subjectField = document.querySelector('#email_campaign_subject_line');
  const fromNameField = document.querySelector('#email_campaign_from_name');
  const fromEmailField = document.querySelector('#email_campaign_from_email');
  const contentField = document.querySelector('#email_campaign_content');
  
  const previewSubject = document.querySelector('#preview-subject');
  const previewFrom = document.querySelector('#preview-from');
  const previewContent = document.querySelector('#preview-content');

  // Update preview when fields change
  function updatePreview() {
    if (subjectField && previewSubject) {
      previewSubject.textContent = subjectField.value || 'Your email subject line...';
    }
    
    if (fromNameField && fromEmailField && previewFrom) {
      const name = fromNameField.value || 'Your Name';
      const email = fromEmailField.value || '<EMAIL>';
      previewFrom.innerHTML = `${name} &lt;${email}&gt;`;
    }
    
    if (contentField && previewContent) {
      const content = contentField.value || 'Start typing your email content to see a preview here...';
      // Simple line break conversion for preview
      const formattedContent = content.replace(/\n/g, '<br>');
      previewContent.innerHTML = formattedContent;
    }
  }

  // Add event listeners
  if (subjectField) subjectField.addEventListener('input', updatePreview);
  if (fromNameField) fromNameField.addEventListener('input', updatePreview);
  if (fromEmailField) fromEmailField.addEventListener('input', updatePreview);
  if (contentField) contentField.addEventListener('input', updatePreview);

  // Character counter for subject line
  if (subjectField) {
    const maxLength = 150;
    const counter = document.createElement('div');
    counter.className = 'text-xs text-slate-500 mt-1 character-counter';
    counter.innerHTML = `<span id="subject-counter">0</span>/${maxLength} characters`;
    
    const parentDiv = subjectField.parentNode;
    const existingHint = parentDiv.querySelector('p');
    if (existingHint) {
      existingHint.remove();
    }
    parentDiv.appendChild(counter);
    
    subjectField.addEventListener('input', function() {
      const length = this.value.length;
      const counterSpan = document.querySelector('#subject-counter');
      if (counterSpan) {
        counterSpan.textContent = length;
        // Change color based on length
        if (length > maxLength * 0.9) {
          counterSpan.className = 'text-amber-600 font-medium';
        } else if (length > maxLength * 0.8) {
          counterSpan.className = 'text-blue-600';
        } else {
          counterSpan.className = 'text-slate-500';
        }
      }
    });
  }

  // Preview text character counter
  const previewTextField = document.querySelector('#email_campaign_preview_text');
  if (previewTextField) {
    const maxLength = 200;
    const counter = document.createElement('div');
    counter.className = 'text-xs text-slate-500 mt-1 character-counter';
    counter.innerHTML = `<span id="preview-counter">0</span>/${maxLength} characters`;
    
    const parentDiv = previewTextField.parentNode;
    const existingHint = parentDiv.querySelector('p');
    if (existingHint) {
      existingHint.remove();
    }
    parentDiv.appendChild(counter);
    
    previewTextField.addEventListener('input', function() {
      const length = this.value.length;
      const counterSpan = document.querySelector('#preview-counter');
      if (counterSpan) {
        counterSpan.textContent = length;
        if (length > maxLength * 0.9) {
          counterSpan.className = 'text-amber-600 font-medium';
        } else if (length > maxLength * 0.8) {
          counterSpan.className = 'text-blue-600';
        } else {
          counterSpan.className = 'text-slate-500';
        }
      }
    });
  }

  // Add loading state for AI generation form
  const aiForm = document.querySelector('form[action*="generate_ai_content"]');
  if (aiForm) {
    aiForm.addEventListener('submit', function() {
      const submitButton = this.querySelector('input[type="submit"]');
      if (submitButton) {
        submitButton.disabled = true;
        const originalValue = submitButton.value;
        submitButton.value = 'Generating...';
        submitButton.className = submitButton.className.replace('from-violet-600 to-purple-600', 'from-slate-400 to-slate-500');
        
        // Add spinner
        const spinner = document.createElement('svg');
        spinner.className = 'animate-spin w-4 h-4 mr-2';
        spinner.innerHTML = '<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>';
        submitButton.style.position = 'relative';
        submitButton.insertBefore(spinner, submitButton.firstChild);
        
        // Reset after timeout if no response
        setTimeout(() => {
          if (submitButton.disabled) {
            submitButton.disabled = false;
            submitButton.value = originalValue;
            submitButton.className = submitButton.className.replace('from-slate-400 to-slate-500', 'from-violet-600 to-purple-600');
            if (spinner.parentNode) {
              spinner.remove();
            }
          }
        }, 30000); // 30 second timeout
      }
    });
  }

  // Email validation for from_email field
  if (fromEmailField) {
    fromEmailField.addEventListener('blur', function() {
      const email = this.value;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      // Remove existing validation message
      const existingMessage = this.parentNode.querySelector('.validation-message');
      if (existingMessage) {
        existingMessage.remove();
      }
      
      if (email && !emailRegex.test(email)) {
        const message = document.createElement('p');
        message.className = 'text-xs text-red-500 mt-1 validation-message';
        message.textContent = 'Please enter a valid email address';
        this.parentNode.appendChild(message);
        this.className = this.className.replace('border-slate-200', 'border-red-300');
      } else {
        this.className = this.className.replace('border-red-300', 'border-slate-200');
      }
    });
  }

  // Auto-save functionality (optional)
  let autoSaveTimeout;
  const mainForm = document.querySelector('form[action*="email_content"]');
  if (mainForm) {
    const formFields = mainForm.querySelectorAll('input[type="text"], input[type="email"], textarea, select');
    
    formFields.forEach(field => {
      field.addEventListener('input', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(() => {
          // Show auto-save indicator
          showAutoSaveIndicator();
        }, 2000);
      });
    });
  }

  function showAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg text-sm shadow-lg z-50';
    indicator.innerHTML = '<span class="flex items-center"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Draft auto-saved</span>';
    document.body.appendChild(indicator);
    
    setTimeout(() => {
      indicator.remove();
    }, 3000);
  }

  // Initial setup
  updatePreview();
  
  // Trigger character counters
  if (subjectField && subjectField.value) {
    subjectField.dispatchEvent(new Event('input'));
  }
  if (previewTextField && previewTextField.value) {
    previewTextField.dispatchEvent(new Event('input'));
  }
});
