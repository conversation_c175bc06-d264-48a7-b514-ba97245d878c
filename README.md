# AI Marketing Hub 🚀

An Enterprise-grade, Multi-tenant AI Marketing Automation Platform built with Ruby on Rails 8.

## Overview

AI Marketing Hub enables small businesses to access automated, enterprise-grade marketing tools through intelligent AI workflows. The platform provides comprehensive marketing automation across email, social media, and SEO channels with advanced data harmonization and multi-tenant architecture.

## 🏗️ Architecture

- **Rails 8.0** - Latest Rails with modern conventions
- **Multi-tenancy** - ActsAsTenant for complete data isolation
- **Real-time UI** - Hotwire (Turbo + Stimulus) for reactive interfaces
- **Styling** - TailwindCSS for rapid, consistent design
- **Background Jobs** - Solid Queue for reliable async processing
- **Database** - PostgreSQL with optimized indexes and constraints
- **Testing** - RSpec with TDD/DDD principles throughout

## 🔧 Technology Stack

### Backend
- **Ruby 3.4.3** with **Rails 8.0.2**
- **PostgreSQL** - Primary database with JSON support
- **Solid Queue** - Background job processing
- **Devise** - Authentication and session management
- **ActsAsTenant** - Multi-tenant data isolation
- **Pundit** - Authorization and access control

### Frontend
- **Hotwire** (Turbo + Stimulus) - SPA-like experience without complexity
- **TailwindCSS** - Utility-first CSS framework
- **ViewComponent** patterns for reusable UI components

### Testing & Quality
- **RSpec** - Behavior-driven testing framework
- **FactoryBot** - Test data factories
- **SimpleCov** - Code coverage reporting (74.42% coverage)
- **RuboCop** - Code style and quality enforcement
- **Brakeman** - Security vulnerability scanning

### AI & External APIs
- **OpenAI GPT-4o** - Content generation and AI workflows
- **External AI microservices** - Specialized AI processing
- **Mailchimp API** - Email marketing integration
- **Social Media APIs** - Multi-platform social management

## 🏛️ Domain Model

### Core Entities

#### Tenant (Aggregate Root)
- Multi-tenant isolation with subdomain routing
- Settings and configuration management
- Status management (active, suspended, cancelled)

#### User
- Devise authentication with tenant scoping
- Role-based permissions (member, admin, owner)
- Multi-factor authentication ready

### Planned Entities
- **Campaign** - Marketing campaign orchestration
- **DataSource** - External data integration
- **AgentWorkflow** - AI agent task coordination
- **Feature** - Centralized feature store

## 🗄️ Database Schema

### Current Tables

```sql
-- Tenants table
CREATE TABLE tenants (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR NOT NULL UNIQUE,
  subdomain VARCHAR NOT NULL UNIQUE,
  status VARCHAR DEFAULT 'active' NOT NULL,
  settings JSONB DEFAULT '{}' NOT NULL,
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);

-- Users table  
CREATE TABLE users (
  id BIGSERIAL PRIMARY KEY,
  email VARCHAR DEFAULT '' NOT NULL,
  encrypted_password VARCHAR DEFAULT '' NOT NULL,
  tenant_id BIGINT NOT NULL REFERENCES tenants(id),
  first_name VARCHAR NOT NULL,
  last_name VARCHAR NOT NULL,
  role INTEGER DEFAULT 0 NOT NULL,
  -- Devise fields...
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);
```

## 🚀 Getting Started

### Prerequisites
- Ruby 3.4.3
- PostgreSQL 14+
- Node.js (for asset compilation)

### Installation

```bash
# Clone the repository
git clone <your-repo-url>
cd ai_marketing_hub

# Install dependencies
bundle install

# Setup database
rails db:create db:migrate

# Configure OpenAI API (Required for AI features)
cp .env.example .env
# Edit .env and add your OpenAI API key:
# OPENAI_API_KEY=sk-your-actual-api-key-here

# Run tests
bundle exec rspec

# Start development server
bin/dev
```

### OpenAI Configuration

AI features require an OpenAI API key. See [OPENAI_SETUP.md](OPENAI_SETUP.md) for detailed setup instructions.

**Quick Setup:**
1. Get API key from [OpenAI Platform](https://platform.openai.com/api-keys)
2. Copy `.env.example` to `.env`
3. Add your API key: `OPENAI_API_KEY=sk-your-key-here`
4. Test configuration: `ruby test_openai_config.rb`

### Development

```bash
# Run tests with coverage
bundle exec rspec

# Start development server with live reloading
bin/dev

# Run background jobs
bundle exec jobs

# Code quality checks
bundle exec rubocop
bundle exec brakeman
```

## 🧪 Testing

Comprehensive test suite built with RSpec following TDD principles:

- **22 passing tests** with **74.42% code coverage**
- **Unit tests** for all domain models
- **Integration tests** for multi-tenant behavior
- **Factory definitions** for consistent test data
- **VCR cassettes** for external API testing

```bash
# Run all tests
bundle exec rspec

# Run with coverage report
bundle exec rspec --coverage

# Run specific test files
bundle exec rspec spec/models/tenant_spec.rb
```

## 🔐 Security Features

- **Multi-tenant data isolation** - Complete separation of tenant data
- **Role-based access control** - Granular permission management
- **Encrypted credentials** - Secure secrets management
- **SQL injection protection** - Parameterized queries only
- **XSS protection** - Content sanitization
- **CSRF protection** - Built-in Rails security

## 📊 Multi-Tenancy

The platform uses **subdomain-based tenancy** with complete data isolation:

```ruby
# Each tenant gets their own subdomain
tenant = Tenant.create!(
  name: "Acme Corp",
  subdomain: "acme-corp"
)
# Access: https://acme-corp.yourdomain.com

# All data is automatically scoped to the current tenant
ActsAsTenant.with_tenant(tenant) do
  users = User.all # Only returns users for this tenant
end
```

## 🤖 AI Agent Architecture (Planned)

The system will implement agentic AI workflows:

```ruby
# Marketing Manager Agent orchestrates specialist agents
class MarketingManagerAgent
  def orchestrate_campaign(requirements)
    # Delegate to specialist agents
    seo_result = SeoSpecialistAgent.optimize(requirements)
    email_result = EmailSpecialistAgent.generate(requirements)
    social_result = SocialMediaAgent.schedule(requirements)
    
    # Aggregate and return results
    CampaignResult.new(seo: seo_result, email: email_result, social: social_result)
  end
end
```

## 🛣️ Roadmap

### Phase 1: Foundation ✅ COMPLETE
- [x] Rails 8 application setup
- [x] Multi-tenant architecture
- [x] User authentication & authorization
- [x] Testing framework
- [x] Database design

### Phase 2: Core Marketing Models (Next)
- [ ] Campaign management system
- [ ] Email campaign builder
- [ ] Social media campaign planner
- [ ] SEO optimization tools

### Phase 3: AI Agent System
- [ ] AI agent orchestration framework
- [ ] Specialist AI agents (Email, SEO, Social)
- [ ] Background job processing
- [ ] Real-time status updates

### Phase 4: Data Integration
- [ ] External data source connectors
- [ ] Data harmonization pipeline
- [ ] Feature store implementation
- [ ] Analytics and reporting

### Phase 5: Production Readiness
- [ ] Deployment automation
- [ ] Monitoring and alerting
- [ ] Performance optimization
- [ ] Security hardening

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Write tests for your changes
4. Ensure all tests pass (`bundle exec rspec`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🔗 Links

- [Rails 8 Guide](https://guides.rubyonrails.org/)
- [Hotwire Documentation](https://hotwired.dev/)
- [TailwindCSS Documentation](https://tailwindcss.com/)
- [ActsAsTenant Documentation](https://github.com/ErwinM/acts_as_tenant)

---

**Built with ❤️ using Rails 8, Hotwire, and modern web technologies.**
