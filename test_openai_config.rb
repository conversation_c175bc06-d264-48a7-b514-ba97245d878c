#!/usr/bin/env ruby

# Test script to verify OpenAI configuration
# Run with: ruby test_openai_config.rb

require 'bundler/setup'
require_relative 'config/environment'

puts "🔍 Testing OpenAI Configuration..."
puts "=" * 50

begin
  # Test 1: Check if credentials are accessible
  puts "1. Checking Rails credentials..."
  credentials_key = Rails.application.credentials.openai_api_key
  if credentials_key
    puts "   ✅ Rails credentials contain OpenAI key: #{credentials_key[0..10]}..."
  else
    puts "   ❌ No OpenAI key found in Rails credentials"
  end

  # Test 2: Check environment variable
  puts "\n2. Checking environment variable..."
  env_key = ENV["OPENAI_API_KEY"]
  if env_key
    puts "   ✅ Environment variable set: #{env_key[0..10]}..."
  else
    puts "   ❌ OPENAI_API_KEY environment variable not set"
  end

  # Test 3: Try to initialize OpenAI service
  puts "\n3. Testing OpenAI service initialization..."
  service = OpenAiService.new
  puts "   ✅ OpenAI service initialized successfully!"
  puts "   📋 Using API key: #{service.instance_variable_get(:@api_key)[0..10]}..."

  # Test 4: Test if we can make a simple request (only if real key)
  api_key = service.instance_variable_get(:@api_key)
  if api_key && !api_key.include?("placeholder")
    puts "\n4. Testing API connection..."
    begin
      result = service.generate_content(
        prompt: "Say 'Hello from AI Marketing Hub!' in exactly 5 words.",
        max_tokens: 20
      )
      puts "   ✅ API connection successful!"
      puts "   📝 Response: #{result}"
    rescue => e
      puts "   ⚠️  API connection failed: #{e.message}"
      puts "   💡 This might be due to invalid API key or network issues"
    end
  else
    puts "\n4. Skipping API test (placeholder key detected)"
    puts "   💡 Replace the placeholder key with a real OpenAI API key to test API connection"
  end

rescue OpenAiService::OpenAiError => e
  puts "   ❌ OpenAI configuration error:"
  puts e.message
rescue => e
  puts "   ❌ Unexpected error: #{e.message}"
  puts "   📍 #{e.backtrace.first}"
end

puts "\n" + "=" * 50
puts "🎯 Configuration Summary:"
puts "   • Rails credentials: #{Rails.application.credentials.openai_api_key ? '✅ Set' : '❌ Not set'}"
puts "   • Environment variable: #{ENV['OPENAI_API_KEY'] ? '✅ Set' : '❌ Not set'}"
puts "\n💡 Next steps:"
puts "   1. If using development: Set OPENAI_API_KEY in .env file"
puts "   2. If using production: Update Rails credentials with real API key"
puts "   3. Get API key from: https://platform.openai.com/api-keys"
