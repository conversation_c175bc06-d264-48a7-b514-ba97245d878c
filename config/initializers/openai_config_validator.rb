# frozen_string_literal: true

# OpenAI Configuration Validator
# This initializer validates OpenAI API key configuration on application startup

Rails.application.configure do
  config.after_initialize do
    # Only validate in development and production, skip in test
    next if Rails.env.test?

    begin
      # Check if OpenAI API key is configured
      api_key = Rails.application.credentials.openai_api_key || ENV["OPENAI_API_KEY"]
      
      if api_key.blank?
        Rails.logger.warn "⚠️  OpenAI API key not configured. AI features will not work."
        Rails.logger.warn "   See OPENAI_SETUP.md for configuration instructions."
      elsif api_key.include?("placeholder")
        Rails.logger.warn "⚠️  OpenAI API key is set to placeholder value."
        Rails.logger.warn "   Replace with real API key from https://platform.openai.com/api-keys"
      else
        Rails.logger.info "✅ OpenAI API key configured successfully."
      end
      
    rescue => e
      Rails.logger.error "❌ Error validating OpenAI configuration: #{e.message}"
    end
  end
end
