# OpenAI API Configuration Guide

This guide explains how to configure the OpenAI API key for the AI Marketing Hub application.

## Quick Setup (Development)

1. **Get your OpenAI API Key**
   - Visit [OpenAI Platform](https://platform.openai.com/api-keys)
   - Create a new API key
   - Copy the key (it starts with `sk-`)

2. **Set up Environment Variable**
   ```bash
   # Copy the example file
   cp .env.example .env
   
   # Edit the .env file and add your API key
   echo "OPENAI_API_KEY=your_actual_api_key_here" >> .env
   ```

3. **Test the Configuration**
   ```bash
   # Start the Rails console
   rails console
   
   # Test the OpenAI service
   service = OpenAiService.new
   puts "✅ OpenAI API key configured successfully!"
   ```

## Production Setup (Rails Credentials)

For production environments, use Rails encrypted credentials:

1. **Edit Rails Credentials**
   ```bash
   rails credentials:edit
   ```

2. **Add the OpenAI API Key**
   ```yaml
   # Add this to your credentials file
   openai_api_key: your_actual_api_key_here
   
   # Keep existing content like:
   secret_key_base: your_existing_secret_key_base
   ```

3. **Deploy with Master Key**
   - Ensure `config/master.key` is available in production
   - Or set `RAILS_MASTER_KEY` environment variable

## Configuration Priority

The application checks for the API key in this order:

1. **Method Parameter** (highest priority)
   ```ruby
   OpenAiService.new(api_key: "sk-your-key")
   ```

2. **Rails Credentials** (recommended for production)
   ```ruby
   Rails.application.credentials.openai_api_key
   ```

3. **Environment Variable** (good for development)
   ```ruby
   ENV["OPENAI_API_KEY"]
   ```

## Troubleshooting

### Error: "OpenAI API key not configured"

This means none of the configuration methods above have been set up. Follow the Quick Setup guide above.

### Error: "OpenAI API error: 401"

This means your API key is invalid or expired:
- Check that your API key is correct
- Verify your OpenAI account has sufficient credits
- Ensure the API key hasn't been revoked

### Error: "OpenAI API rate limit exceeded"

Your API usage has exceeded the rate limits:
- Wait a few minutes and try again
- Consider upgrading your OpenAI plan
- Implement request throttling in your application

## Security Best Practices

1. **Never commit API keys to version control**
2. **Use different API keys for different environments**
3. **Regularly rotate your API keys**
4. **Monitor your API usage and costs**
5. **Use Rails credentials for production deployments**

## Testing the Setup

After configuration, test with:

```ruby
# In Rails console
service = OpenAiService.new
result = service.generate_content(prompt: "Hello, this is a test!")
puts result
```

If successful, you should see a response from OpenAI.
