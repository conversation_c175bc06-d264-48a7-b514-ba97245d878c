# frozen_string_literal: true

require 'rails_helper'

RSpec.describe OpenAiService, type: :service do
  describe '#initialize' do
    context 'when API key is provided as parameter' do
      it 'uses the provided API key' do
        service = described_class.new(api_key: 'test-key')
        expect(service.instance_variable_get(:@api_key)).to eq('test-key')
      end
    end

    context 'when API key is in Rails credentials' do
      before do
        allow(Rails.application.credentials).to receive(:openai_api_key).and_return('credentials-key')
      end

      it 'uses the credentials API key' do
        service = described_class.new
        expect(service.instance_variable_get(:@api_key)).to eq('credentials-key')
      end
    end

    context 'when API key is in environment variable' do
      before do
        allow(Rails.application.credentials).to receive(:openai_api_key).and_return(nil)
        allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return('env-key')
      end

      it 'uses the environment variable API key' do
        service = described_class.new
        expect(service.instance_variable_get(:@api_key)).to eq('env-key')
      end
    end

    context 'when no API key is configured' do
      before do
        allow(Rails.application.credentials).to receive(:openai_api_key).and_return(nil)
        allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return(nil)
      end

      it 'raises an OpenAiError with helpful message' do
        expect { described_class.new }.to raise_error(OpenAiService::OpenAiError) do |error|
          expect(error.message).to include('OpenAI API key not configured')
          expect(error.message).to include('rails credentials:edit')
          expect(error.message).to include('OPENAI_API_KEY=')
          expect(error.message).to include('https://platform.openai.com/api-keys')
        end
      end
    end
  end

  describe '#generate_content' do
    let(:service) { described_class.new(api_key: 'test-key') }
    let(:mock_response) do
      {
        'choices' => [
          {
            'message' => {
              'content' => 'Generated content response'
            }
          }
        ]
      }
    end

    before do
      allow(service).to receive(:make_request).and_return(mock_response)
    end

    it 'generates content with default parameters' do
      result = service.generate_content(prompt: 'Test prompt')
      expect(result).to eq('Generated content response')
    end

    it 'passes custom parameters to the API' do
      expect(service).to receive(:make_request).with(
        endpoint: '/chat/completions',
        payload: hash_including(
          model: 'gpt-3.5-turbo',
          temperature: 0.5,
          max_tokens: 1000,
          messages: array_including(
            hash_including(role: 'user', content: 'Custom prompt')
          )
        )
      )

      service.generate_content(
        prompt: 'Custom prompt',
        model: 'gpt-3.5-turbo',
        temperature: 0.5,
        max_tokens: 1000
      )
    end
  end

  describe 'API key priority' do
    it 'prioritizes parameter over credentials over environment' do
      allow(Rails.application.credentials).to receive(:openai_api_key).and_return('credentials-key')
      allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return('env-key')

      service = described_class.new(api_key: 'param-key')
      expect(service.instance_variable_get(:@api_key)).to eq('param-key')
    end

    it 'prioritizes credentials over environment when no parameter' do
      allow(Rails.application.credentials).to receive(:openai_api_key).and_return('credentials-key')
      allow(ENV).to receive(:[]).with('OPENAI_API_KEY').and_return('env-key')

      service = described_class.new
      expect(service.instance_variable_get(:@api_key)).to eq('credentials-key')
    end
  end
end
